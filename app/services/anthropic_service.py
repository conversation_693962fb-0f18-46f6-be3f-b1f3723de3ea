import anthropic
from typing import Dict, Any, List
from app.core.config import settings
from app.schemas.prd import PRDFormData

class AnthropicService:
    def __init__(self):
        self.client = anthropic.Anthropic(api_key=settings.ANTHROPIC_API_KEY)
    
    async def generate_prd(self, form_data: PRDFormData, user_context: dict) -> str:
        prompt = self._build_prompt(form_data, user_context)
        
        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=4000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )
            
            return "".join([
                block.text for block in message.content 
                if hasattr(block, 'text')
            ])
            
        except Exception as e:
            raise Exception(f"Failed to generate PRD: {str(e)}")
    
    def _build_prompt(self, form_data: PRDFormData, user_context: dict) -> str:
        return f"""You are an expert Product Manager creating a PRD for {user_context.get('tenant_name', 'the organization')}.

**Core Information:**
1. Product/Feature: {form_data.question1}
2. Target Users & Problem: {form_data.question2}
3. Key Functionality & Success Metrics: {form_data.question3}

{f"**Technical Constraints:** {form_data.technical_constraints}" if form_data.technical_constraints else ""}
{f"**Competitive Analysis:** {form_data.competitive_analysis}" if form_data.competitive_analysis else ""}
{f"**Risks and Mitigation:** {form_data.risks_and_mitigation}" if form_data.risks_and_mitigation else ""}
{f"**Resource Requirements:** {form_data.resource_requirements}" if form_data.resource_requirements else ""}
{f"**User Personas:** {form_data.user_personas}" if form_data.user_personas else ""}
{f"**Acceptance Criteria:** {form_data.acceptance_criteria}" if form_data.acceptance_criteria else ""}

Create a comprehensive PRD following this structure:

# PRD: [Feature Name]

## 1.0 Document Control
[Include table with feature details]

## 2.0 Overview and Business Case
[What is this feature, problem statement, business goals]

## 3.0 User Goals and Stories
[Target users, user goals, user stories, non-goals]

## 4.0 Functional Requirements
[Core features by priority]

## 5.0 User Experience
[User journey, key interactions, edge cases]

## 6.0 Technical Requirements
[API specs, integrations, performance, security]

## 7.0 Success Metrics
[Primary KPIs, secondary metrics, success criteria]

## 8.0 Implementation Plan
[Phases, dependencies, risks]

Generate a detailed, professional PRD with specific, actionable content."""

    async def generate_document(self, form_data: Dict[str, Any], document_type, user_context: dict) -> str:
        """
        Generate document content based on document type and form data
        """
        prompt = self._build_document_prompt(form_data, document_type, user_context)

        # Use the document type's AI agent configuration
        ai_config = document_type.ai_agents
        model = ai_config.get("primary_agent", "claude-3-5-sonnet-20241022")

        try:
            message = await self.client.messages.create(
                model=model,
                max_tokens=4000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            return "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

        except Exception as e:
            raise Exception(f"Failed to generate {document_type.name}: {str(e)}")

    def _build_document_prompt(self, form_data: Dict[str, Any], document_type, user_context: dict) -> str:
        """
        Build prompt based on document type template and form data
        """
        template_structure = document_type.template_structure

        # Build form data section
        form_data_text = ""
        for key, value in form_data.items():
            if value:
                form_data_text += f"**{key.replace('_', ' ').title()}:** {value}\n\n"

        # Build template structure
        template_text = ""
        if "sections" in template_structure:
            for section in template_structure["sections"]:
                template_text += f"## {section.get('title', 'Section')}\n"
                template_text += f"{section.get('description', '[Content for this section]')}\n\n"

        prompt = f"""You are an expert creating a {document_type.name} for {user_context.get('tenant_name', 'the organization')}.

**Document Type:** {document_type.name}
**Category:** {document_type.category}
**Description:** {document_type.description or 'Professional document'}

**Input Information:**
{form_data_text}

**Required Structure:**
{template_text if template_text else "Create a well-structured, professional document with appropriate sections and detailed content."}

Generate a comprehensive, professional {document_type.name} with specific, actionable content.
Follow the structure provided and ensure all sections are thoroughly detailed and relevant to the input information."""

        return prompt

    async def generate_followup_questions(self, form_data: Dict[str, Any], content: str, context: str = None) -> List[Dict[str, Any]]:
        """
        Generate follow-up questions to improve document quality
        """
        prompt = f"""Based on the following document content and original form data, generate 3-5 follow-up questions that would help improve the document quality and completeness.

**Original Form Data:**
{self._format_form_data(form_data)}

**Current Document Content:**
{content[:2000]}...

**Additional Context:**
{context or 'No additional context provided'}

Generate questions that would help:
1. Fill in missing details
2. Clarify ambiguous points
3. Add more specific examples
4. Improve technical accuracy
5. Enhance user experience considerations

Return the questions in this JSON format:
[
  {{
    "id": "q1",
    "question": "Question text here?",
    "type": "text",
    "required": true,
    "context": "Why this question helps improve the document"
  }}
]

Focus on actionable questions that would lead to concrete improvements."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=1500,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            response_text = "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

            # Extract JSON from response
            import json
            import re

            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                questions_json = json_match.group()
                questions = json.loads(questions_json)
                return questions
            else:
                # Fallback to default questions
                return [
                    {
                        "id": "q1",
                        "question": "Are there any additional details or requirements that should be included?",
                        "type": "text",
                        "required": False,
                        "context": "General improvement question"
                    }
                ]

        except Exception as e:
            # Return default questions on error
            return [
                {
                    "id": "q1",
                    "question": "Are there any additional details or requirements that should be included?",
                    "type": "text",
                    "required": False,
                    "context": "General improvement question"
                }
            ]

    async def enhance_document_with_followup(self, content: str, form_data: Dict[str, Any], followup_answers: Dict[str, Any], user_context: dict) -> str:
        """
        Enhance document content with follow-up answers
        """
        followup_text = ""
        for key, value in followup_answers.items():
            if value:
                followup_text += f"**{key}:** {value}\n\n"

        prompt = f"""Enhance the following document by incorporating the additional information provided in the follow-up answers. Maintain the original structure and style while seamlessly integrating the new details.

**Original Document:**
{content}

**Additional Information from Follow-up:**
{followup_text}

**Instructions:**
1. Integrate the follow-up information naturally into the existing content
2. Maintain the document's original structure and formatting
3. Ensure consistency in tone and style
4. Add the new information in the most appropriate sections
5. If new sections are needed, add them logically

Return the enhanced document with all improvements integrated."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=4000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            return "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

        except Exception as e:
            raise Exception(f"Failed to enhance document with follow-up: {str(e)}")

    async def regenerate_document_content(self, original_content: str, form_data: Dict[str, Any], followup_answers: Dict[str, Any], user_context: dict) -> str:
        """
        Regenerate document content while preserving structure
        """
        followup_text = ""
        for key, value in followup_answers.items():
            if value:
                followup_text += f"**{key}:** {value}\n\n"

        instructions = user_context.get("regeneration_instructions", "")
        preserve_structure = user_context.get("preserve_structure", True)

        prompt = f"""Regenerate the following document with the updated information and instructions provided.

**Original Document:**
{original_content}

**Updated Form Data:**
{self._format_form_data(form_data)}

**Additional Information:**
{followup_text}

**Regeneration Instructions:**
{instructions or 'Improve the document quality and completeness'}

**Requirements:**
{'1. Preserve the original document structure and section organization' if preserve_structure else '1. Feel free to reorganize and restructure as needed'}
2. Incorporate all new information provided
3. Follow the regeneration instructions carefully
4. Maintain professional tone and quality
5. Ensure all content is accurate and relevant

Generate a comprehensive, improved version of the document."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=4000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            return "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

        except Exception as e:
            raise Exception(f"Failed to regenerate document: {str(e)}")

    def _format_form_data(self, form_data: Dict[str, Any]) -> str:
        """
        Format form data for display in prompts
        """
        formatted = ""
        for key, value in form_data.items():
            if value:
                formatted += f"**{key.replace('_', ' ').title()}:** {value}\n\n"
        return formatted

    async def refine_section(self, section_content: str, refinement_type: str, instructions: str, parameters: Dict[str, Any]) -> str:
        """
        Refine a specific section of content
        """
        prompt = f"""Refine the following section content based on the specified refinement type and instructions.

**Section Content:**
{section_content}

**Refinement Type:** {refinement_type}
**Instructions:** {instructions or 'Apply the specified refinement type'}

**Parameters:**
- Target Length: {parameters.get('target_length', 'maintain current')}
- Tone: {parameters.get('tone', 'maintain current')}
- Section Name: {parameters.get('section_name', 'Unknown')}

**Refinement Guidelines:**
1. Maintain the core meaning and purpose of the section
2. Apply the requested refinement type appropriately
3. Follow the specified tone and length requirements
4. Ensure the refined content flows naturally
5. Preserve any important technical details or requirements

Return only the refined section content without any additional commentary."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            return "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

        except Exception as e:
            raise Exception(f"Failed to refine section: {str(e)}")

    async def refine_component(self, component_content: str, refinement_type: str, instructions: str, parameters: Dict[str, Any]) -> str:
        """
        Refine a specific component of content
        """
        prompt = f"""Refine the following component content based on the specified refinement type and instructions.

**Component Content:**
{component_content}

**Refinement Type:** {refinement_type}
**Instructions:** {instructions or 'Apply the specified refinement type'}

**Parameters:**
{self._format_parameters(parameters)}

**Refinement Guidelines:**
1. Maintain the component's structure and purpose
2. Apply the requested refinement type appropriately
3. Follow any specific parameters provided
4. Ensure the refined content integrates well with surrounding content
5. Preserve any formatting or structural elements

Return only the refined component content without any additional commentary."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=1500,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            return "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

        except Exception as e:
            raise Exception(f"Failed to refine component: {str(e)}")

    async def custom_refinement(self, content: str, instructions: str, parameters: Dict[str, Any]) -> str:
        """
        Apply custom refinement to content
        """
        target_sections = parameters.get('target_sections', [])
        preserve_formatting = parameters.get('preserve_formatting', True)

        prompt = f"""Apply the following custom refinement to the document content.

**Document Content:**
{content}

**Refinement Instructions:**
{instructions}

**Target Sections:** {', '.join(target_sections) if target_sections else 'Entire document'}
**Preserve Formatting:** {preserve_formatting}

**Guidelines:**
1. Follow the custom instructions precisely
2. {'Maintain the existing formatting and structure' if preserve_formatting else 'Feel free to restructure as needed'}
3. {'Focus only on the specified sections' if target_sections else 'Apply refinement to the entire document'}
4. Ensure the refined content maintains coherence and flow
5. Preserve any critical information or requirements

Return the complete refined document content."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=4000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            return "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

        except Exception as e:
            raise Exception(f"Failed to apply custom refinement: {str(e)}")

    async def generate_refinement_suggestions(self, content: str, form_data: Dict[str, Any], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate AI-powered refinement suggestions
        """
        prompt = f"""Analyze the following document and generate specific, actionable refinement suggestions.

**Document Content:**
{content[:3000]}...

**Original Form Data:**
{self._format_form_data(form_data)}

**Context:**
- Document Status: {context.get('current_status', 'draft')}
- Document Type: {context.get('document_type', 'unknown')}

Generate 3-7 specific refinement suggestions that would improve the document quality. For each suggestion, provide:
1. A clear, actionable suggestion
2. The type of refinement (section, component, or global)
3. The target area (section name, component id, or "document")
4. A description of why this improvement would be beneficial
5. The estimated impact (low, medium, high)

Return suggestions in this JSON format:
[
  {{
    "id": "suggestion_1",
    "type": "section",
    "target": "Executive Summary",
    "suggestion": "Expand the executive summary with more specific metrics and outcomes",
    "description": "The current summary is too brief and lacks concrete details that would help stakeholders understand the impact",
    "impact": "high",
    "estimated_improvement": "Better stakeholder understanding and buy-in"
  }}
]

Focus on practical improvements that would make the document more effective, clear, and comprehensive."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2000,
                temperature=0.4,
                messages=[{"role": "user", "content": prompt}]
            )

            response_text = "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

            # Extract JSON from response
            import json
            import re

            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                suggestions_json = json_match.group()
                suggestions = json.loads(suggestions_json)
                return suggestions
            else:
                # Return default suggestions if parsing fails
                return [
                    {
                        "id": "general_improvement",
                        "type": "global",
                        "target": "document",
                        "suggestion": "Review and improve overall document clarity and structure",
                        "description": "General improvements to enhance readability and effectiveness",
                        "impact": "medium",
                        "estimated_improvement": "Better overall document quality"
                    }
                ]

        except Exception as e:
            # Return default suggestions on error
            return [
                {
                    "id": "error_fallback",
                    "type": "global",
                    "target": "document",
                    "suggestion": "Review document for potential improvements",
                    "description": "Manual review recommended due to analysis error",
                    "impact": "low",
                    "estimated_improvement": "Potential quality improvements"
                }
            ]

    async def apply_suggestions(self, content: str, suggestion_ids: List[str], custom_instructions: str = None) -> str:
        """
        Apply selected refinement suggestions to content
        """
        prompt = f"""Apply the selected refinement suggestions to the following document content.

**Document Content:**
{content}

**Selected Suggestion IDs:** {', '.join(suggestion_ids)}
**Additional Instructions:** {custom_instructions or 'Apply the selected suggestions as specified'}

**Guidelines:**
1. Apply only the improvements corresponding to the selected suggestion IDs
2. Maintain the document's overall structure and purpose
3. Ensure all changes integrate smoothly with existing content
4. Follow any additional custom instructions provided
5. Preserve critical information and requirements

Return the complete improved document content."""

        try:
            message = await self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=4000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            return "".join([
                block.text for block in message.content
                if hasattr(block, 'text')
            ])

        except Exception as e:
            raise Exception(f"Failed to apply suggestions: {str(e)}")

    def _format_parameters(self, parameters: Dict[str, Any]) -> str:
        """
        Format parameters for display in prompts
        """
        if not parameters:
            return "None specified"

        formatted = ""
        for key, value in parameters.items():
            formatted += f"- {key.replace('_', ' ').title()}: {value}\n"

        return formatted

anthropic_service = AnthropicService()