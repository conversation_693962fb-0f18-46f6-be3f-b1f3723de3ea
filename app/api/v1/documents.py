from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document, DocumentVersion, DocumentGenerationSession, DocumentRefinementJob
from app.models.document_type import DocumentType
from app.schemas.document import (
    DocumentResponse,
    DocumentListResponse,
    DocumentGenerate,
    DocumentCreate,
    DocumentUpdate,
    DocumentStatusUpdate,
    DocumentVersionResponse,
    DocumentDuplicateRequest,
    DocumentConvertRequest,
    MessageResponse,
    FormSubmitRequest,
    FormValidationRequest,
    FormValidationResponse,
    FormSchemaResponse,
    FollowUpQuestionsRequest,
    FollowUpQuestionsResponse,
    FollowUpAnswersRequest,
    GenerationStatus,
    RegenerateRequest,
    SectionRefinementRequest,
    ComponentRefinementRequest,
    CustomRefinementRequest,
    RefinementOptionsResponse,
    RefinementSuggestionsResponse,
    ApplySuggestionsRequest,
    RefinementJobResponse
)
from app.services.anthropic_service import anthropic_service

router = APIRouter()

@router.post("/generate", response_model=DocumentResponse)
async def generate_document(
    document_data: DocumentGenerate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Generate new document (any type)
    """
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == document_data.document_type_id,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    try:
        # Generate document content using AI
        user_context = {
            "tenant_name": current_tenant.name,
            "user_name": current_user.full_name,
            "document_type": document_type.name
        }
        
        # Use the document type's template and AI configuration
        content = await anthropic_service.generate_document(
            document_data.form_data,
            document_type,
            user_context
        )
        
        # Extract title from content or use provided title
        title = document_data.title or f"New {document_type.name}"
        
        # Create document
        document = Document(
            title=title,
            content=content,
            document_type_id=document_data.document_type_id,
            form_data=document_data.form_data,
            user_id=current_user.id,
            tenant_id=current_tenant.id
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        # Create initial version
        version = DocumentVersion(
            document_id=document.id,
            version="1.0",
            version_number=1,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Initial version",
            created_by=current_user.id
        )
        
        db.add(version)
        
        # Update document type usage count
        document_type.usage_count += 1
        
        db.commit()
        
        return document
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate document: {str(e)}"
        )

@router.get("/", response_model=List[DocumentListResponse])
async def list_documents(
    document_type_id: Optional[uuid.UUID] = Query(None, description="Filter by document type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, le=100, description="Number of documents to return"),
    offset: int = Query(0, description="Number of documents to skip"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List user's documents
    """
    query = db.query(Document).filter(
        Document.tenant_id == current_tenant.id
    )
    
    # Filter by document type if specified
    if document_type_id:
        query = query.filter(Document.document_type_id == document_type_id)
    
    # Filter by status if specified
    if status:
        query = query.filter(Document.status == status)
    
    # Apply pagination and ordering
    documents = query.order_by(desc(Document.updated_at)).offset(offset).limit(limit).all()
    
    return documents

@router.get("/{doc_id}", response_model=DocumentResponse)
async def get_document(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Update view count and last viewed
    document.view_count += 1
    document.last_viewed_at = datetime.utcnow()
    db.commit()

    return document

@router.put("/{doc_id}", response_model=DocumentResponse)
async def update_document(
    doc_id: uuid.UUID,
    document_update: DocumentUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update document content
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check if user has edit permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to edit this document"
        )

    # Store original values for version history
    original_title = document.title
    original_content = document.content
    original_form_data = document.form_data

    # Update document
    update_data = document_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(document, field, value)

    # Increment version if content changed
    content_changed = (
        document_update.content and document_update.content != original_content
    )

    if content_changed:
        # Create new version
        latest_version = db.query(DocumentVersion).filter(
            DocumentVersion.document_id == doc_id
        ).order_by(desc(DocumentVersion.version_number)).first()

        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        new_version = f"{new_version_number}.0"

        document.version = new_version
        document.version_number = new_version_number

        # Create version record
        version = DocumentVersion(
            document_id=document.id,
            version=new_version,
            version_number=new_version_number,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Content updated",
            created_by=current_user.id
        )

        db.add(version)

    db.commit()
    db.refresh(document)

    return document

@router.delete("/{doc_id}", response_model=MessageResponse)
async def delete_document(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check if user has permission to delete
    if (document.user_id != current_user.id and current_user.role != "admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to delete this document"
        )

    # Soft delete - archive the document
    document.status = "archived"
    db.commit()

    return {"message": "Document deleted successfully"}

@router.post("/{doc_id}/duplicate", response_model=DocumentResponse)
async def duplicate_document(
    doc_id: uuid.UUID,
    duplicate_request: DocumentDuplicateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Duplicate existing document
    """
    original_document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not original_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check if user has view permissions
    if (original_document.user_id != current_user.id and
        current_user.id not in original_document.permissions.get("can_view", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to duplicate this document"
        )

    # Create duplicate
    new_title = duplicate_request.title or f"Copy of {original_document.title}"

    duplicate = Document(
        title=new_title,
        content=original_document.content,
        document_type_id=original_document.document_type_id,
        form_data=original_document.form_data,
        status="draft",
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        permissions=original_document.permissions if duplicate_request.copy_permissions else {}
    )

    db.add(duplicate)
    db.commit()
    db.refresh(duplicate)

    # Create initial version for duplicate
    version = DocumentVersion(
        document_id=duplicate.id,
        version="1.0",
        version_number=1,
        title=duplicate.title,
        content=duplicate.content,
        form_data=duplicate.form_data,
        status=duplicate.status,
        change_summary=f"Duplicated from document {original_document.id}",
        created_by=current_user.id
    )

    db.add(version)
    db.commit()

    return duplicate

@router.put("/{doc_id}/status", response_model=DocumentResponse)
async def update_document_status(
    doc_id: uuid.UUID,
    status_update: DocumentStatusUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update document status
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update document status"
        )

    old_status = document.status
    document.status = status_update.status

    # Set published_at if status is being set to published
    if status_update.status == "published" and old_status != "published":
        document.published_at = datetime.utcnow()

    # Create version record for status change
    latest_version = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).first()

    new_version_number = (latest_version.version_number + 1) if latest_version else 1
    new_version = f"{new_version_number}.0"

    document.version = new_version
    document.version_number = new_version_number

    version = DocumentVersion(
        document_id=document.id,
        version=new_version,
        version_number=new_version_number,
        title=document.title,
        content=document.content,
        form_data=document.form_data,
        status=document.status,
        change_summary=status_update.change_summary or f"Status changed from {old_status} to {status_update.status}",
        created_by=current_user.id
    )

    db.add(version)
    db.commit()
    db.refresh(document)

    return document

@router.get("/{doc_id}/history", response_model=List[DocumentVersionResponse])
async def get_document_history(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get document version history
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_view", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view document history"
        )

    versions = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).all()

    return versions

@router.post("/{doc_id}/restore/{version}", response_model=DocumentResponse)
async def restore_document_version(
    doc_id: uuid.UUID,
    version: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Restore previous version
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to restore document version"
        )

    # Find the version to restore
    version_to_restore = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id,
        DocumentVersion.version == version
    ).first()

    if not version_to_restore:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Version not found"
        )

    # Restore the document to the specified version
    document.title = version_to_restore.title
    document.content = version_to_restore.content
    document.form_data = version_to_restore.form_data
    document.status = version_to_restore.status

    # Create new version record for the restore
    latest_version = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).first()

    new_version_number = (latest_version.version_number + 1) if latest_version else 1
    new_version = f"{new_version_number}.0"

    document.version = new_version
    document.version_number = new_version_number

    restore_version = DocumentVersion(
        document_id=document.id,
        version=new_version,
        version_number=new_version_number,
        title=document.title,
        content=document.content,
        form_data=document.form_data,
        status=document.status,
        change_summary=f"Restored to version {version}",
        created_by=current_user.id
    )

    db.add(restore_version)
    db.commit()
    db.refresh(document)

    return document

@router.post("/{doc_id}/convert/{target_type}", response_model=DocumentResponse)
async def convert_document_type(
    doc_id: uuid.UUID,
    target_type: uuid.UUID,
    convert_request: DocumentConvertRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Convert to different document type
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to convert document"
        )

    # Get target document type
    target_document_type = db.query(DocumentType).filter(
        DocumentType.id == target_type,
        DocumentType.is_active == True
    ).first()

    if not target_document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Target document type not found"
        )

    # Update document type
    old_type_id = document.document_type_id
    document.document_type_id = target_type

    # Update form data if provided
    if convert_request.update_form_data:
        document.form_data = convert_request.update_form_data

    # Optionally regenerate content based on new type
    if not convert_request.preserve_content:
        try:
            user_context = {
                "tenant_name": current_tenant.name,
                "user_name": current_user.full_name,
                "document_type": target_document_type.name
            }

            document.content = await anthropic_service.generate_document(
                document.form_data,
                target_document_type,
                user_context
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to regenerate content: {str(e)}"
            )

    # Create version record for conversion
    latest_version = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).first()

    new_version_number = (latest_version.version_number + 1) if latest_version else 1
    new_version = f"{new_version_number}.0"

    document.version = new_version
    document.version_number = new_version_number

    version = DocumentVersion(
        document_id=document.id,
        version=new_version,
        version_number=new_version_number,
        title=document.title,
        content=document.content,
        form_data=document.form_data,
        status=document.status,
        change_summary=f"Converted from document type {old_type_id} to {target_type}",
        created_by=current_user.id
    )

    db.add(version)
    db.commit()
    db.refresh(document)

    return document

@router.get("/by-type/{type_id}", response_model=List[DocumentListResponse])
async def list_documents_by_type(
    type_id: uuid.UUID,
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, le=100, description="Number of documents to return"),
    offset: int = Query(0, description="Number of documents to skip"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List documents of specific type
    """
    # Verify document type exists and user has access
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    query = db.query(Document).filter(
        Document.tenant_id == current_tenant.id,
        Document.document_type_id == type_id
    )

    # Filter by status if specified
    if status:
        query = query.filter(Document.status == status)

    # Apply pagination and ordering
    documents = query.order_by(desc(Document.updated_at)).offset(offset).limit(limit).all()

    return documents

# Document Generation Workflow APIs

@router.post("/form/submit", response_model=DocumentResponse)
async def submit_form(
    form_request: FormSubmitRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Submit form for any document type
    """
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == form_request.document_type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Validate form data
    validation_result = _validate_form_data(form_request.form_data, document_type)
    if not validation_result["is_valid"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Form validation failed: {validation_result['errors']}"
        )

    if form_request.save_as_draft:
        # Create draft document without generating content
        title = form_request.title or f"Draft {document_type.name}"
        document = Document(
            title=title,
            content="",  # Empty content for draft
            document_type_id=form_request.document_type_id,
            form_data=form_request.form_data,
            status="draft",
            user_id=current_user.id,
            tenant_id=current_tenant.id
        )

        db.add(document)
        db.commit()
        db.refresh(document)

        return document
    else:
        # Generate document content
        try:
            user_context = {
                "tenant_name": current_tenant.name,
                "user_name": current_user.full_name,
                "document_type": document_type.name
            }

            content = await anthropic_service.generate_document(
                form_request.form_data,
                document_type,
                user_context
            )

            title = form_request.title or f"New {document_type.name}"

            document = Document(
                title=title,
                content=content,
                document_type_id=form_request.document_type_id,
                form_data=form_request.form_data,
                user_id=current_user.id,
                tenant_id=current_tenant.id
            )

            db.add(document)
            db.commit()
            db.refresh(document)

            # Create initial version
            version = DocumentVersion(
                document_id=document.id,
                version="1.0",
                version_number=1,
                title=document.title,
                content=document.content,
                form_data=document.form_data,
                status=document.status,
                change_summary="Initial version",
                created_by=current_user.id
            )

            db.add(version)

            # Update document type usage count
            document_type.usage_count += 1

            db.commit()

            return document

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate document: {str(e)}"
            )

@router.post("/form/validate", response_model=FormValidationResponse)
async def validate_form(
    validation_request: FormValidationRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Validate form data against document type schema
    """
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == validation_request.document_type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    validation_result = _validate_form_data(validation_request.form_data, document_type)

    return FormValidationResponse(
        is_valid=validation_result["is_valid"],
        errors=validation_result["errors"],
        warnings=validation_result.get("warnings", [])
    )

@router.get("/form/schema/{type_id}", response_model=FormSchemaResponse)
async def get_form_schema(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get form schema for document type
    """
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    schema = document_type.form_schema
    required_fields = []
    optional_fields = []

    # Extract required and optional fields from schema
    if "fields" in schema:
        for field in schema["fields"]:
            if field.get("required", False):
                required_fields.append(field["name"])
            else:
                optional_fields.append(field["name"])

    return FormSchemaResponse(
        document_type_id=type_id,
        form_schema=schema,
        required_fields=required_fields,
        optional_fields=optional_fields
    )

@router.post("/{doc_id}/followup-questions", response_model=FollowUpQuestionsResponse)
async def get_followup_questions(
    doc_id: uuid.UUID,
    request: FollowUpQuestionsRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get AI follow-up questions
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to get follow-up questions"
        )

    try:
        # Generate follow-up questions using AI
        questions = await anthropic_service.generate_followup_questions(
            document.form_data,
            document.content,
            request.context
        )

        # Create generation session
        session = DocumentGenerationSession(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            session_type="followup",
            original_form_data=document.form_data,
            followup_questions=questions
        )

        db.add(session)
        db.commit()
        db.refresh(session)

        return FollowUpQuestionsResponse(
            questions=questions,
            session_id=str(session.id)
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate follow-up questions: {str(e)}"
        )

@router.post("/{doc_id}/followup-answers", response_model=DocumentResponse)
async def submit_followup_answers(
    doc_id: uuid.UUID,
    answers_request: FollowUpAnswersRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Submit follow-up answers
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Find the generation session
    session = db.query(DocumentGenerationSession).filter(
        DocumentGenerationSession.id == answers_request.session_id,
        DocumentGenerationSession.document_id == doc_id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generation session not found"
        )

    try:
        # Update session with answers
        session.followup_answers = answers_request.answers
        session.status = "processing"

        # Regenerate document with follow-up answers
        document_type = db.query(DocumentType).filter(
            DocumentType.id == document.document_type_id
        ).first()

        user_context = {
            "tenant_name": current_tenant.name,
            "user_name": current_user.full_name,
            "document_type": document_type.name
        }

        enhanced_content = await anthropic_service.enhance_document_with_followup(
            document.content,
            document.form_data,
            answers_request.answers,
            user_context
        )

        # Update document
        document.content = enhanced_content

        # Create new version
        latest_version = db.query(DocumentVersion).filter(
            DocumentVersion.document_id == doc_id
        ).order_by(desc(DocumentVersion.version_number)).first()

        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        new_version = f"{new_version_number}.0"

        document.version = new_version
        document.version_number = new_version_number

        version = DocumentVersion(
            document_id=document.id,
            version=new_version,
            version_number=new_version_number,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Enhanced with follow-up answers",
            created_by=current_user.id
        )

        db.add(version)

        # Update session
        session.status = "completed"
        session.completed_at = datetime.utcnow()
        session.generated_content = enhanced_content

        db.commit()
        db.refresh(document)

        return document

    except Exception as e:
        session.status = "failed"
        session.error_message = str(e)
        db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process follow-up answers: {str(e)}"
        )

@router.get("/{doc_id}/generation-status", response_model=GenerationStatus)
async def get_generation_status(
    doc_id: uuid.UUID,
    session_id: Optional[str] = Query(None, description="Specific session ID to check"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Check generation progress
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Get the latest or specific generation session
    query = db.query(DocumentGenerationSession).filter(
        DocumentGenerationSession.document_id == doc_id
    )

    if session_id:
        session = query.filter(DocumentGenerationSession.id == session_id).first()
    else:
        session = query.order_by(desc(DocumentGenerationSession.started_at)).first()

    if not session:
        # No generation session found, assume document is completed
        return GenerationStatus(
            status="completed",
            progress=100,
            message="Document generation completed"
        )

    return GenerationStatus(
        status=session.status,
        progress=session.progress,
        message=f"Generation {session.status}",
        estimated_completion=session.completed_at,
        error_details=session.error_message
    )

@router.post("/{doc_id}/regenerate", response_model=DocumentResponse)
async def regenerate_document(
    doc_id: uuid.UUID,
    regenerate_request: RegenerateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Regenerate document with new inputs
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to regenerate document"
        )

    try:
        # Create regeneration session
        session = DocumentGenerationSession(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            session_type="regeneration",
            original_form_data=document.form_data,
            followup_answers=regenerate_request.followup_answers or {},
            generation_instructions=regenerate_request.regeneration_instructions,
            status="processing"
        )

        db.add(session)
        db.commit()

        # Use updated form data if provided
        form_data = regenerate_request.form_data or document.form_data

        # Get document type
        document_type = db.query(DocumentType).filter(
            DocumentType.id == document.document_type_id
        ).first()

        user_context = {
            "tenant_name": current_tenant.name,
            "user_name": current_user.full_name,
            "document_type": document_type.name,
            "regeneration_instructions": regenerate_request.regeneration_instructions,
            "preserve_structure": regenerate_request.preserve_structure
        }

        # Regenerate content
        if regenerate_request.preserve_structure:
            new_content = await anthropic_service.regenerate_document_content(
                document.content,
                form_data,
                regenerate_request.followup_answers or {},
                user_context
            )
        else:
            new_content = await anthropic_service.generate_document(
                form_data,
                document_type,
                user_context
            )

        # Update document
        document.content = new_content
        if regenerate_request.form_data:
            document.form_data = form_data

        # Create new version
        latest_version = db.query(DocumentVersion).filter(
            DocumentVersion.document_id == doc_id
        ).order_by(desc(DocumentVersion.version_number)).first()

        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        new_version = f"{new_version_number}.0"

        document.version = new_version
        document.version_number = new_version_number

        version = DocumentVersion(
            document_id=document.id,
            version=new_version,
            version_number=new_version_number,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Document regenerated",
            created_by=current_user.id
        )

        db.add(version)

        # Update session
        session.status = "completed"
        session.progress = 100
        session.completed_at = datetime.utcnow()
        session.generated_content = new_content

        db.commit()
        db.refresh(document)

        return document

    except Exception as e:
        if 'session' in locals():
            session.status = "failed"
            session.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate document: {str(e)}"
        )



# Document Refinement APIs

@router.post("/{doc_id}/refine/section/{section_name}", response_model=RefinementJobResponse)
async def refine_section(
    doc_id: uuid.UUID,
    section_name: str,
    refinement_request: SectionRefinementRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Refine specific section of document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to refine document"
        )

    try:
        # Create refinement job
        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="section",
            target=section_name,
            refinement_type=refinement_request.refinement_type,
            instructions=refinement_request.instructions,
            parameters={
                "target_length": refinement_request.target_length,
                "tone": refinement_request.tone
            },
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        # Extract section content
        section_content = _extract_section_content(document.content, section_name)
        if not section_content:
            job.status = "failed"
            job.error_message = f"Section '{section_name}' not found in document"
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Section '{section_name}' not found in document"
            )

        job.original_content = section_content

        # Perform refinement using AI
        refined_content = await anthropic_service.refine_section(
            section_content,
            refinement_request.refinement_type,
            refinement_request.instructions,
            {
                "target_length": refinement_request.target_length,
                "tone": refinement_request.tone,
                "section_name": section_name
            }
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.utcnow()

        db.commit()

        return RefinementJobResponse(
            job=job,
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refine section: {str(e)}"
        )

@router.post("/{doc_id}/refine/component/{component_id}", response_model=RefinementJobResponse)
async def refine_component(
    doc_id: uuid.UUID,
    component_id: str,
    refinement_request: ComponentRefinementRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Refine specific component of document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to refine document"
        )

    try:
        # Create refinement job
        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="component",
            target=component_id,
            refinement_type=refinement_request.refinement_type,
            instructions=refinement_request.instructions,
            parameters=refinement_request.parameters or {},
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        # Extract component content
        component_content = _extract_component_content(document.content, component_id)
        if not component_content:
            job.status = "failed"
            job.error_message = f"Component '{component_id}' not found in document"
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Component '{component_id}' not found in document"
            )

        job.original_content = component_content

        # Perform refinement using AI
        refined_content = await anthropic_service.refine_component(
            component_content,
            refinement_request.refinement_type,
            refinement_request.instructions,
            refinement_request.parameters or {}
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.utcnow()

        db.commit()

        return RefinementJobResponse(
            job=job,
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refine component: {str(e)}"
        )

@router.get("/{doc_id}/refinement-options", response_model=RefinementOptionsResponse)
async def get_refinement_options(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get available refinement options for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Get document type for specific refinement options
    document_type = db.query(DocumentType).filter(
        DocumentType.id == document.document_type_id
    ).first()

    # Extract sections and components from document
    sections = _extract_document_sections(document.content)
    components = _extract_document_components(document.content)

    # Get available refinements from document type
    available_refinements = []
    if document_type and document_type.refinement_options:
        refinement_config = document_type.refinement_options

        # Add section refinements
        for refinement in refinement_config.get("section_refinements", []):
            available_refinements.append({
                "id": refinement["id"],
                "name": refinement["name"],
                "description": refinement["description"],
                "type": "section",
                "applicable_to": sections,
                "parameters": refinement.get("parameters", {})
            })

        # Add component refinements
        for refinement in refinement_config.get("component_refinements", []):
            available_refinements.append({
                "id": refinement["id"],
                "name": refinement["name"],
                "description": refinement["description"],
                "type": "component",
                "applicable_to": [comp["id"] for comp in components],
                "parameters": refinement.get("parameters", {})
            })

        # Add global refinements
        for refinement in refinement_config.get("global_refinements", []):
            available_refinements.append({
                "id": refinement["id"],
                "name": refinement["name"],
                "description": refinement["description"],
                "type": "global",
                "applicable_to": ["document"],
                "parameters": refinement.get("parameters", {})
            })

    # Add default refinements if none configured
    if not available_refinements:
        available_refinements = [
            {
                "id": "expand",
                "name": "Expand Content",
                "description": "Add more detail and examples",
                "type": "section",
                "applicable_to": sections,
                "parameters": {}
            },
            {
                "id": "clarify",
                "name": "Clarify Content",
                "description": "Make content clearer and easier to understand",
                "type": "section",
                "applicable_to": sections,
                "parameters": {}
            },
            {
                "id": "improve_tone",
                "name": "Improve Tone",
                "description": "Adjust tone and style",
                "type": "global",
                "applicable_to": ["document"],
                "parameters": {"tone_options": ["professional", "casual", "technical"]}
            }
        ]

    return RefinementOptionsResponse(
        available_refinements=available_refinements,
        document_sections=sections,
        document_components=components
    )

@router.post("/{doc_id}/refine/custom", response_model=RefinementJobResponse)
async def custom_refinement(
    doc_id: uuid.UUID,
    refinement_request: CustomRefinementRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Apply custom refinement to document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to refine document"
        )

    try:
        # Create refinement job
        target = ",".join(refinement_request.target_sections) if refinement_request.target_sections else "document"

        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="custom",
            target=target,
            refinement_type=refinement_request.refinement_type,
            instructions=refinement_request.instructions,
            parameters={
                "target_sections": refinement_request.target_sections,
                "preserve_formatting": refinement_request.preserve_formatting
            },
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        job.original_content = document.content

        # Perform custom refinement using AI
        refined_content = await anthropic_service.custom_refinement(
            document.content,
            refinement_request.instructions,
            {
                "target_sections": refinement_request.target_sections,
                "preserve_formatting": refinement_request.preserve_formatting,
                "document_type": document.document_type_id
            }
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.utcnow()

        db.commit()

        return RefinementJobResponse(
            job=job,
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply custom refinement: {str(e)}"
        )

@router.get("/{doc_id}/refinement-suggestions", response_model=RefinementSuggestionsResponse)
async def get_refinement_suggestions(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get AI-generated refinement suggestions
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    try:
        # Generate suggestions using AI
        suggestions = await anthropic_service.generate_refinement_suggestions(
            document.content,
            document.form_data,
            {
                "document_type": document.document_type_id,
                "current_status": document.status
            }
        )

        return RefinementSuggestionsResponse(
            suggestions=suggestions,
            total_suggestions=len(suggestions)
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate suggestions: {str(e)}"
        )

@router.post("/{doc_id}/apply-suggestions", response_model=RefinementJobResponse)
async def apply_suggestions(
    doc_id: uuid.UUID,
    apply_request: ApplySuggestionsRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Apply selected refinement suggestions
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to apply suggestions"
        )

    try:
        # Create refinement job
        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="suggestions",
            target="document",
            refinement_type="apply_suggestions",
            instructions=apply_request.custom_instructions,
            parameters={
                "suggestion_ids": apply_request.suggestion_ids
            },
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        job.original_content = document.content

        # Apply suggestions using AI
        refined_content = await anthropic_service.apply_suggestions(
            document.content,
            apply_request.suggestion_ids,
            apply_request.custom_instructions
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.utcnow()

        db.commit()

        return RefinementJobResponse(
            job=job,
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply suggestions: {str(e)}"
        )

@router.get("/{doc_id}/refinement-status/{job_id}", response_model=RefinementJobResponse)
async def get_refinement_status(
    doc_id: uuid.UUID,
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Check refinement job progress
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    job = db.query(DocumentRefinementJob).filter(
        DocumentRefinementJob.id == job_id,
        DocumentRefinementJob.document_id == doc_id,
        DocumentRefinementJob.tenant_id == current_tenant.id
    ).first()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Refinement job not found"
        )

    return RefinementJobResponse(
        job=job,
        result=job.refined_content if job.status == "completed" else None
    )

@router.post("/{doc_id}/refinement/cancel/{job_id}", response_model=MessageResponse)
async def cancel_refinement(
    doc_id: uuid.UUID,
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Cancel refinement job
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    job = db.query(DocumentRefinementJob).filter(
        DocumentRefinementJob.id == job_id,
        DocumentRefinementJob.document_id == doc_id,
        DocumentRefinementJob.user_id == current_user.id
    ).first()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Refinement job not found"
        )

    if job.status in ["completed", "failed", "cancelled"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot cancel job with status: {job.status}"
        )

    job.status = "cancelled"
    job.cancelled_at = datetime.utcnow()
    db.commit()

    return {"message": "Refinement job cancelled successfully"}

# Helper functions for refinement

def _extract_section_content(content: str, section_name: str) -> str:
    """
    Extract content of a specific section from document
    """
    import re

    # Try different section header patterns
    patterns = [
        rf"#{1,6}\s*{re.escape(section_name)}\s*\n(.*?)(?=\n#{1,6}\s|\Z)",
        rf"^{re.escape(section_name)}\s*\n[=-]+\s*\n(.*?)(?=\n\w+\s*\n[=-]+|\Z)",
        rf"^{re.escape(section_name)}:\s*\n(.*?)(?=\n\w+:|\Z)"
    ]

    for pattern in patterns:
        match = re.search(pattern, content, re.MULTILINE | re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()

    return ""

def _extract_component_content(content: str, component_id: str) -> str:
    """
    Extract content of a specific component from document
    """
    import re

    # Try to find component by ID or marker
    patterns = [
        rf"<!--\s*{re.escape(component_id)}\s*-->(.*?)<!--\s*/\s*{re.escape(component_id)}\s*-->",
        rf"\[{re.escape(component_id)}\](.*?)\[/{re.escape(component_id)}\]",
        rf"<{re.escape(component_id)}>(.*?)</{re.escape(component_id)}>"
    ]

    for pattern in patterns:
        match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()

    return ""

def _extract_document_sections(content: str) -> List[str]:
    """
    Extract all section names from document
    """
    import re

    sections = []

    # Find markdown headers
    header_matches = re.findall(r'^#{1,6}\s*(.+?)$', content, re.MULTILINE)
    sections.extend([match.strip() for match in header_matches])

    # Find underlined headers
    underline_matches = re.findall(r'^(.+?)\s*\n[=-]+\s*$', content, re.MULTILINE)
    sections.extend([match.strip() for match in underline_matches])

    # Find colon-style headers
    colon_matches = re.findall(r'^(.+?):\s*$', content, re.MULTILINE)
    sections.extend([match.strip() for match in colon_matches])

    # Remove duplicates and return
    return list(set(sections))

def _extract_document_components(content: str) -> List[Dict[str, str]]:
    """
    Extract all components from document
    """
    import re

    components = []

    # Find HTML-style components
    html_matches = re.findall(r'<!--\s*(\w+)\s*-->', content)
    for match in html_matches:
        components.append({"id": match, "type": "html_comment"})

    # Find bracket-style components
    bracket_matches = re.findall(r'\[(\w+)\]', content)
    for match in bracket_matches:
        components.append({"id": match, "type": "bracket"})

    # Find XML-style components
    xml_matches = re.findall(r'<(\w+)>', content)
    for match in xml_matches:
        components.append({"id": match, "type": "xml"})

    # Remove duplicates
    seen = set()
    unique_components = []
    for comp in components:
        comp_key = f"{comp['id']}_{comp['type']}"
        if comp_key not in seen:
            seen.add(comp_key)
            unique_components.append(comp)

    return unique_components
